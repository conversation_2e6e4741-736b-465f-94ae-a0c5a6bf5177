/**
 * Bildirim Sistemi JavaScript
 * Bu dosya, bildirim butonu ve sidebar fonksiyonalitesini yönetir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

(function() {
    'use strict';

    // DOM yüklendikten sonra çalıştır
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationSystem();
    });

    /**
     * Bildirim sistemini başlat
     */
    function initNotificationSystem() {
        const notificationButton = document.getElementById('tutor-notification-button');
        const notificationOverlay = document.getElementById('tutor-notification-overlay');
        const notificationSidebar = document.getElementById('tutor-notification-sidebar');
        const notificationClose = document.getElementById('tutor-notification-close');

        // Elementlerin varlığını kontrol et
        if (!notificationButton || !notificationOverlay || !notificationSidebar || !notificationClose) {
            console.warn('Bildirim sistemi elementleri bulunamadı');
            return;
        }

        // Event listener'ları ekle
        setupEventListeners(notificationButton, notificationOverlay, notificationSidebar, notificationClose);

        // Klavye erişilebilirliği
        setupKeyboardAccessibility(notificationOverlay, notificationClose);

        console.log('Bildirim sistemi başarıyla başlatıldı');
    }

    /**
     * Event listener'ları ayarla
     */
    function setupEventListeners(button, overlay, sidebar, closeBtn) {
        // Bildirim butonuna tıklama
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, true);
        });

        // Kapatma butonuna tıklama
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, false);
        });

        // Overlay'e tıklama (sidebar dışına tıklama)
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Escape tuşu ile kapatma
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && overlay.classList.contains('active')) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Sayfa scroll'unu engelle (sidebar açıkken)
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });
    }

    /**
     * Klavye erişilebilirliğini ayarla
     */
    function setupKeyboardAccessibility(overlay, closeBtn) {
        // Tab tuşu ile focus yönetimi
        overlay.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                trapFocus(e, overlay);
            }
        });

        // İlk focus'u kapatma butonuna ver
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                closeBtn.focus();
            }
        });
    }

    /**
     * Focus'u sidebar içinde tut
     */
    function trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Bildirim sidebar'ını aç/kapat
     */
    function toggleNotificationSidebar(overlay, sidebar, show) {
        const button = document.getElementById('tutor-notification-button');

        if (show) {
            // Sidebar'ı aç
            overlay.classList.add('active');

            // Animasyon için kısa gecikme
            setTimeout(() => {
                sidebar.classList.add('active');
            }, 10);

            // Buton aktif durumunu ekle
            if (button) {
                button.classList.add('active');
            }

            // Buton animasyonu
            animateNotificationButton(true);

            // Aria durumunu güncelle
            updateAriaStates(true);

        } else {
            // Sidebar'ı kapat
            sidebar.classList.remove('active');
            overlay.classList.remove('active');

            // Buton aktif durumunu kaldır
            if (button) {
                button.classList.remove('active');
            }

            // Buton animasyonu
            animateNotificationButton(false);

            // Aria durumunu güncelle
            updateAriaStates(false);

            // Body scroll'unu geri yükle
            document.body.style.overflow = '';
        }
    }

    /**
     * Bildirim butonuna animasyon ekle
     */
    function animateNotificationButton(isOpening) {
        const button = document.getElementById('tutor-notification-button');
        if (!button) return;

        if (isOpening) {
            // Açılma animasyonu
            button.style.transform = 'scale(0.9)';
            button.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        } else {
            // Kapanma animasyonu
            button.style.transform = 'scale(1.1)';
            button.style.backgroundColor = '';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * ARIA durumlarını güncelle
     */
    function updateAriaStates(isOpen) {
        const button = document.getElementById('tutor-notification-button');
        const overlay = document.getElementById('tutor-notification-overlay');

        if (button) {
            button.setAttribute('aria-expanded', isOpen.toString());
            button.setAttribute('aria-label', isOpen ? 'Bildirimleri Kapat' : 'Bildirimleri Aç');
        }

        if (overlay) {
            overlay.setAttribute('aria-hidden', (!isOpen).toString());
        }
    }

    /**
     * Bildirim sayısını güncelle (gelecekte kullanılacak)
     */
    function updateNotificationCount(count) {
        const badge = document.getElementById('tutor-notification-badge');
        if (!badge) return;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    /**
     * Bildirim listesini güncelle
     */
    function updateNotificationList(notifications) {
        const list = document.getElementById('tutor-notification-list');
        const empty = document.getElementById('tutor-notification-empty');
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');

        if (!list || !empty || !markAllBtn) return;

        if (notifications && notifications.length > 0) {
            // Bildirimleri göster
            empty.style.display = 'none';
            list.style.display = 'block';
            markAllBtn.style.display = 'flex';

            // Bildirim HTML'ini oluştur
            list.innerHTML = generateNotificationHTML(notifications);

            // Duyuru tıklama olaylarını ekle
            attachAnnouncementClickEvents();
        } else {
            // Boş durumu göster
            empty.style.display = 'flex';
            list.style.display = 'none';
            markAllBtn.style.display = 'none';
        }
    }

    /**
     * Duyuru HTML'ini oluştur
     */
    function generateNotificationHTML(announcements) {
        let html = '';

        announcements.forEach(function(announcement) {
            const isRead = announcement.is_read ? 'read' : 'unread';
            const readClass = announcement.is_read ? 'tutor-notification-read' : '';

            html += `
                <div class="tutor-notification-item ${readClass}" data-announcement-id="${announcement.id}">
                    <div class="tutor-notification-item-header">
                        <div class="tutor-notification-avatar">
                            <img src="${announcement.author_avatar}" alt="${announcement.author_name}" />
                        </div>
                        <div class="tutor-notification-meta">
                            <div class="tutor-notification-course">${announcement.course_title}</div>
                            <div class="tutor-notification-time">${announcement.date_human}</div>
                        </div>
                        <div class="tutor-notification-status ${isRead}">
                            ${announcement.is_read ? '' : '<span class="tutor-notification-dot"></span>'}
                        </div>
                    </div>
                    <div class="tutor-notification-content">
                        <h4 class="tutor-notification-title">${announcement.title}</h4>
                        <p class="tutor-notification-excerpt">${announcement.content}</p>
                        <div class="tutor-notification-author">
                            <span class="tutor-notification-by">Tarafından:</span>
                            <span class="tutor-notification-author-name">${announcement.author_name}</span>
                        </div>
                    </div>
                    <div class="tutor-notification-actions">
                        <a href="${announcement.course_url}" class="tutor-notification-view-course" target="_blank">
                            Kursu Görüntüle
                        </a>
                    </div>
                </div>
            `;
        });

        return html;
    }

    /**
     * Duyuru tıklama olaylarını ekle
     */
    function attachAnnouncementClickEvents() {
        const notificationItems = document.querySelectorAll('.tutor-notification-item:not(.tutor-notification-read)');

        notificationItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const announcementId = this.getAttribute('data-announcement-id');
                markAnnouncementAsRead(announcementId, this);
            });
        });
    }

    /**
     * Duyuruyu okundu olarak işaretle
     */
    function markAnnouncementAsRead(announcementId, element) {
        // AJAX ile duyuruyu okundu olarak işaretle
        const formData = new FormData();
        formData.append('action', 'dmr_lms_mark_announcement_read');
        formData.append('announcement_id', announcementId);
        formData.append('nonce', _tutorobject.nonce);

        fetch(ajaxurl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Element'i okundu olarak işaretle
                element.classList.add('tutor-notification-read');
                const statusDot = element.querySelector('.tutor-notification-dot');
                if (statusDot) {
                    statusDot.remove();
                }

                // Bildirim sayısını güncelle
                updateNotificationCountFromDOM();
            }
        })
        .catch(error => {
            console.error('Duyuru işaretleme hatası:', error);
        });
    }

    /**
     * DOM'dan bildirim sayısını güncelle
     */
    function updateNotificationCountFromDOM() {
        const unreadItems = document.querySelectorAll('.tutor-notification-item:not(.tutor-notification-read)');
        const count = unreadItems.length;
        updateNotificationCount(count);
    }

    /**
     * Kullanıcının kurs duyurularını yükle
     */
    function loadUserAnnouncements() {
        const formData = new FormData();
        formData.append('action', 'dmr_lms_get_user_announcements');
        formData.append('limit', 10);
        formData.append('nonce', _tutorobject.nonce);

        fetch(ajaxurl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const announcements = data.data.announcements;
                const unreadCount = announcements.filter(a => !a.is_read).length;

                // Bildirim listesini güncelle
                updateNotificationList(announcements);

                // Bildirim sayısını güncelle
                updateNotificationCount(unreadCount);
            } else {
                console.error('Duyuru yükleme hatası:', data.data);
                // Boş liste göster
                updateNotificationList([]);
                updateNotificationCount(0);
            }
        })
        .catch(error => {
            console.error('AJAX hatası:', error);
            // Boş liste göster
            updateNotificationList([]);
            updateNotificationCount(0);
        });
    }

    /**
     * Bildirim sidebar'ı açıldığında duyuruları yükle
     */
    function onSidebarOpen() {
        loadUserAnnouncements();
    }

    /**
     * Global fonksiyonları window objesine ekle
     */
    window.TutorNotificationSystem = {
        updateCount: updateNotificationCount,
        updateList: updateNotificationList,
        loadAnnouncements: loadUserAnnouncements,
        toggle: function(show) {
            const overlay = document.getElementById('tutor-notification-overlay');
            const sidebar = document.getElementById('tutor-notification-sidebar');
            if (overlay && sidebar) {
                toggleNotificationSidebar(overlay, sidebar, show);

                // Sidebar açıldığında duyuruları yükle
                if (show) {
                    onSidebarOpen();
                }
            }
        }
    };

    /**
     * Sadece duyuru sayısını yükle (sayfa yüklendiğinde)
     */
    function loadAnnouncementCount() {
        const formData = new FormData();
        formData.append('action', 'dmr_lms_get_user_announcements');
        formData.append('limit', 50); // Daha fazla duyuru kontrol et
        formData.append('nonce', _tutorobject.nonce);

        fetch(ajaxurl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const announcements = data.data.announcements;
                const unreadCount = announcements.filter(a => !a.is_read).length;

                // Sadece bildirim sayısını güncelle
                updateNotificationCount(unreadCount);
            }
        })
        .catch(error => {
            console.error('Duyuru sayısı yükleme hatası:', error);
        });
    }

    // DOM yüklendiğinde bildirim sistemini başlat
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationSystem();

        // Sayfa yüklendiğinde duyuru sayısını yükle (sadece sayı)
        loadAnnouncementCount();
    });

})();
